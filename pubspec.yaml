name: wordle
description: More than a word game.

# Prevent accidental publishing to pub.dev.
publish_to: "none"

version: 2.1.4+2024092516
environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get_it: ^7.2.0
  shared_preferences: ^2.0.8
  flutter_dotenv: ^5.0.2
  dio: ^4.0.1
  # dio_http_cache: ^0.3.0
  json_annotation: ^4.4.0
  mobx: 2.1.3
  flutter_mobx: 2.0.6+5
  auto_size_text: ^3.0.0
  flutter_conditional_rendering: ^2.0.0
  flutter_overlay_loader: ^1.0.3
  permission_handler: ^10.2.0
  flutter_svg: ^1.0.3
  oktoast: ^3.1.5
  flutter_markdown: ^0.6.9
  firebase_analytics: ^11.0.0
  firebase_core: ^3.0.0
  firebase_messaging: ^15.0.0
  firebase_crashlytics: ^4.0.0
  firebase_in_app_messaging: ^0.8.0
  firebase_remote_config: ^5.0.0
  firebase_auth: ^5.0.0
  firebase_database: ^11.0.0
  firebase_app_check: ^0.3.0
  firebase_core_platform_interface: ^6.0.0
  cloud_functions: ^5.0.0
  cloud_firestore: ^5.0.0
  flutter_local_notifications: ^9.2.0
  in_app_review: ^2.0.4
  share_plus: ^11.0.0
  path_provider: ^2.0.9
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.1.1
  twitter_login: ^4.1.0-dev
  sign_in_with_apple: ^7.0.1
  flutter_signin_button: ^2.0.0
  crypto: ^3.0.1
  font_awesome_flutter: ^9.2.0
  in_app_purchase: ^3.0.1
  shimmer: ^2.0.0
  lottie: ^1.2.2
  url_launcher: ^6.0.20
  webview_flutter: ^4.0.5
  google_mobile_ads: ^5.1.0
  badges: ^3.0.2
  cached_network_image: 3.2.0
  sizer: ^2.0.15
  community_charts_flutter: ^1.0.0
  responsive_framework: ^0.2.0
  expandable_page_view: ^1.0.17
  app_links: ^6.4.0
  in_app_notification: ^1.1.2
  dropdown_button2: ^2.3.9
  fluttertoast: ^8.2.5
  dotted_border: ^2.1.0
  http: ^1.2.1
  # ironsource_mediation: ^1.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0
  json_serializable: ^6.6.1
  build_runner:
  mobx_codegen: 2.1.1
  flutter_launcher_icons: "^0.13.1"



flutter_launcher_icons:
  ios: true
  android: "launcher_icon"
  image_path: "assets/images/new_logo_grid.png"
  adaptive_icon_background: '#000000'
  remove_alpha_ios: true
  min_sdk_android: 21



flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    # Add assets from the images directory to the application.
    - assets/images/
    - assets/icons/
    - assets/json/
    - assets/docs/
    - config/
    - assets/fonts/

  fonts:
    - family: Clear Sans
      fonts:
        - asset: assets/fonts/clear-sans.regular.ttf
          weight: 400
          style: normal
        - asset: assets/fonts/clear-sans.bold.ttf
          weight: 700
    - family: Stymie
      fonts:
        - asset: assets/fonts/stymie/STYMIEM.ttf
          weight: 400
          style: normal
        - asset: assets/fonts/stymie/STYMIEL.ttf
          weight: 300
        - asset: assets/fonts/stymie/STYMIEB.ttf
          weight: 700
        - asset: assets/fonts/stymie/STYMIEXB.ttf
          weight: 900
    - family: Urbanist
      fonts:
        - asset: assets/fonts/urbanist/Urbanist-Light.ttf
          weight: 300
        - asset: assets/fonts/urbanist/Urbanist-Regular.ttf
          weight: 400
          style: normal
        - asset: assets/fonts/urbanist/Urbanist-Medium.ttf
          weight: 500
        - asset: assets/fonts/urbanist/Urbanist-Bold.ttf
          weight: 600
        - asset: assets/fonts/urbanist/Urbanist-ExtraBold.ttf
          weight: 900

